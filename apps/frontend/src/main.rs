#![warn(clippy::all, rust_2018_idioms)]
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")] // hide console window on Windows in release

// When compiling natively:
#[cfg(not(target_arch = "wasm32"))]
fn main() -> eframe::Result {
    env_logger::init(); // Log to stderr (if you run with `RUST_LOG=debug`).

    let native_options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([400.0, 300.0])
            .with_min_inner_size([300.0, 220.0])
            .with_icon(
                // NOTE: Adding an icon is optional
                eframe::icon_data::from_png_bytes(&include_bytes!("../assets/icon-256.png")[..])
                    .expect("Failed to load icon"),
            ),
        ..Default::default()
    };
    eframe::run_native(
        "eframe template",
        native_options,
        Box::new(|cc| Ok(Box::new(eframe_template::WardaFrontend::new(cc)))),
    )
}

// When compiling to web using trunk:
#[cfg(target_arch = "wasm32")]
fn main() {
    use eframe::wasm_bindgen::JsCast as _;

    // Redirect `log` message to `console.log` and friends:
    eframe::WebLogger::init(log::LevelFilter::Debug).ok();

    // Debug: Confirm we're running the updated WASM code
    web_sys::console::log_1(&"🚀 WASM: Starting main function with authentication fixes".into());

    // Check if window.authenticationReady exists at all
    let window = web_sys::window().expect("No window");
    let auth_ready = js_sys::Reflect::get(&window, &"authenticationReady".into()).unwrap_or(wasm_bindgen::JsValue::UNDEFINED);
    web_sys::console::log_2(&"🔍 WASM: window.authenticationReady at startup:".into(), &auth_ready);

    let web_options = eframe::WebOptions::default();

    wasm_bindgen_futures::spawn_local(async {
        // Wait for authentication to complete before starting the app
        wait_for_authentication().await;

        let document = web_sys::window()
            .expect("No window")
            .document()
            .expect("No document");

        let canvas = document
            .get_element_by_id("the_canvas_id")
            .expect("Failed to find the_canvas_id")
            .dyn_into::<web_sys::HtmlCanvasElement>()
            .expect("the_canvas_id was not a HtmlCanvasElement");

        let start_result = eframe::WebRunner::new()
            .start(
                canvas.id().as_str(),
                web_options,
                Box::new(|cc| Ok(Box::new(eframe_template::WardaFrontend::new(cc)))),
            )
            .await;

        // Remove the loading text and spinner:
        if let Some(loading_text) = document.get_element_by_id("loading_text") {
            match start_result {
                Ok(_) => {
                    loading_text.remove();
                }
                Err(e) => {
                    loading_text.set_inner_html(
                        "<p> The app has crashed. See the developer console for details. </p>",
                    );
                    panic!("Failed to start eframe: {e:?}");
                }
            }
        }
    });
}

#[cfg(target_arch = "wasm32")]
async fn wait_for_authentication() {
    use wasm_bindgen::prelude::*;
    use wasm_bindgen_futures::JsFuture;
    use web_sys::js_sys;

    // Use both log::info and direct console.log to ensure visibility
    log::info!("🔍 WASM: Waiting for authentication...");
    web_sys::console::log_1(&"🔍 WASM: Waiting for authentication...".into());

    let window = web_sys::window().expect("No window");

    // First check if authentication is already ready (in case of race condition)
    let auth_ready = js_sys::Reflect::get(&window, &JsValue::from_str("authenticationReady"))
        .unwrap_or(JsValue::FALSE);

    if auth_ready.as_bool().unwrap_or(false) {
        log::info!("🚀 WASM: Authentication already ready, proceeding");
        web_sys::console::log_1(&"🚀 WASM: Authentication already ready, proceeding".into());
        return;
    }

    log::info!("⏳ WASM: Authentication not ready yet, waiting for event...");
    web_sys::console::log_1(&"⏳ WASM: Authentication not ready yet, waiting for event...".into());

    // Wait for the authentication event with a timeout fallback
    let auth_promise = js_sys::Promise::new(&mut |resolve, _reject| {
        let resolve_clone = resolve.clone();

        let callback = Closure::wrap(Box::new(move |_event: web_sys::Event| {
            log::info!("✅ WASM: Received authenticationComplete event");
            web_sys::console::log_1(&"✅ WASM: Received authenticationComplete event".into());
            resolve_clone.call0(&JsValue::NULL).unwrap();
        }) as Box<dyn FnMut(_)>);

        window
            .add_event_listener_with_callback("authenticationComplete", callback.as_ref().unchecked_ref())
            .unwrap();

        // Keep the callback alive
        callback.forget();
    });

    // Also set up a polling fallback in case the event was missed
    let polling_promise = js_sys::Promise::new(&mut |resolve, _reject| {
        let resolve_clone = resolve.clone();
        let window_clone = window.clone();

        let poll_callback = Closure::wrap(Box::new(move || {
            let auth_ready = js_sys::Reflect::get(&window_clone, &JsValue::from_str("authenticationReady"))
                .unwrap_or(JsValue::FALSE);

            if auth_ready.as_bool().unwrap_or(false) {
                log::info!("🔄 WASM: Authentication detected via polling fallback");
                web_sys::console::log_1(&"🔄 WASM: Authentication detected via polling fallback".into());
                resolve_clone.call0(&JsValue::NULL).unwrap();
            }
        }) as Box<dyn FnMut()>);

        // Poll every 500ms as fallback
        let interval_id = window.set_interval_with_callback_and_timeout_and_arguments_0(
            poll_callback.as_ref().unchecked_ref(),
            500
        ).unwrap();

        // Store the interval ID to clear it later (though we'll forget the callback)
        poll_callback.forget();
    });

    // Race between event and polling - whichever resolves first wins
    let _ = JsFuture::from(js_sys::Promise::race(&js_sys::Array::of2(&auth_promise, &polling_promise))).await;

    log::info!("🎉 WASM: Authentication wait complete, starting WASM app");
    web_sys::console::log_1(&"🎉 WASM: Authentication wait complete, starting WASM app".into());
}